from odoo import fields, models, api, _
from odoo.exceptions import UserError
import requests
import base64

class central_production(models.Model):
    _name = 'central.production'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _description = 'Central Production'


    partner_id = fields.Many2one('res.partner', string='Client', required=True, tracking=True)
    company = fields.Many2one('res.partner', string="Société")
    user_id = fields.Many2one('res.users', string='Commercial', required=True, tracking=True)
    puissance = fields.Float(string='Puissance', required=True, tracking=True)
    date = fields.Date(string='Date', required=True, tracking=True, default=fields.Date.today)
    
    adresse_terrain = fields.Char(string='Adresse du terrain')
    montant_total = fields.Float(string='Montant total')
    montant_financement_pers = fields.Float(string='Montant financement opérationnel')
    montant_finance_banc = fields.Float(string='Montant financement bancaire')
    form_soum = fields.Char(string='URL FORMULAIRE DE SOUMISSION A1')

    form_soum_upload = fields.Binary(string='Upload FORMULAIRE DE SOUMISSION A1')
    form_soum_upload_filename = fields.Char(string='Nom du fichier (FORMULAIRE DE SOUMISSION A1)')
    rapport_tech_econom = fields.Char(string='Rapport technico-économique')
    rapport_tech_econom_upload = fields.Binary(string='Upload Rapport technico-économique')
    rapport_tech_econom_upload_filename = fields.Char(string='Nom du fichier (Rapport technico-économique)')
    cap_fin_port = fields.Char(string='Nom du fichier (Rapport technico-économique)')
    cap_fin_port_upload = fields.Binary(string='Upload Capacité financière porteur A2')
    cap_fin_port_upload_filename = fields.Char(string='Nom du fichier (Capacité financière porteur A2)')
    gar_banc = fields.Char(string='Nom du fichier (Garantie bancaire A4)')
    gar_banc_upload = fields.Binary(string='Upload Garantie bancaire A4')
    gar_banc_upload_filename = fields.Char(string='Nom du fichier (Garantie bancaire A4)')
    sourc_fin = fields.Char(string='Source de financement A5')
    sourc_fin_upload = fields.Binary(string='Upload Source de financement A5')
    sourc_fin_upload_filename = fields.Char(string='Nom du fichier (Source de financement A5)')
    ref_tech_gpc = fields.Char(string='Référence technique GPC A3')
    ref_tech_gpc_upload = fields.Binary(string='Upload Référence technique GPC A3')
    ref_tech_gpc_upload_filename = fields.Char(string='Nom du fichier (Référence technique GPC A3)')
    dem_avis = fields.Char(string='Demande d’avis')
    dem_avis_upload = fields.Binary(string='Upload Demande d’avis')
    dem_avis_upload_filename = fields.Char(string='Nom du fichier (Demande d’avis)')
    disp_site = fields.Char(string='Disponibilité site Accord Ministere Agriculture A6')
    disp_site_upload = fields.Binary(string='Upload Disponibilité site Accord Ministere Agriculture A6')
    disp_site_upload_filename = fields.Char(string='Nom du fichier (Disponibilité site Accord Ministere Agriculture A6)')
    dem_acc_prel = fields.Char(string='Demande d’avis accord préliminaire')
    dem_acc_prel_upload = fields.Binary(string='Upload Demande d’avis accord préliminaire')
    dem_acc_prel_upload_filename = fields.Char(string='Nom du fichier (Demande d’avis accord préliminaire)')
    accor_steg = fields.Char(string='Accord STEG A7')
    accor_steg_upload = fields.Binary(string='Upload Accord STEG A7')
    accor_steg_upload_filename = fields.Char(string='Nom du fichier (Accord STEG A7)')
    plan_terr = fields.Char(string='Plan de terrain')
    plan_terr_upload = fields.Binary(string='Upload Plan de terrain')
    plan_terr_upload_filename = fields.Char(string='Nom du fichier (Plan de terrain)')
    photos = fields.Char(string='Photos')
    photos_upload = fields.Binary(string='Upload Photos')
    photos_upload_filename = fields.Char(string='Nom du fichier (Photos)')
    sim_pvsyst = fields.Char(string='Simulation PVsyst')
    sim_pvsyst_upload = fields.Binary(string='Upload Simulation PVsyst')
    sim_pvsyst_upload_filename = fields.Char(string='Nom du fichier (Simulation PVsyst)')
    etud_env = fields.Char(string='Etude environnementale A8')
    etud_env_upload = fields.Binary(string='Upload Etude environnementale A8')
    etud_env_upload_filename = fields.Char(string='Nom du fichier (Etude environnementale A8)')
    plan_trav = fields.Char(string='Planning de travaux A9')
    plan_trav_upload = fields.Binary(string='Upload Planning de travaux A9')
    plan_trav_upload_filename = fields.Char(string='Nom du fichier (Planning de travaux A9)')
    cah_charge_steg = fields.Char(string='Cahier des charges STEG A10')
    cah_charge_steg_upload = fields.Binary(string='Upload Cahier des charges STEG A10')
    cah_charge_steg_upload_filename = fields.Char(string='Nom du fichier (Cahier des charges STEG A10)')
    etud_tech = fields.Char(string='Etude technique A11')
    etud_tech_upload = fields.Binary(string='Upload Etude technique A11')
    etud_tech_upload_filename = fields.Char(string='Nom du fichier (Etude technique A11)')
    contrat = fields.Char(string='Contrat')
    contrat_upload = fields.Binary(string='Upload Contrat')
    contrat_upload_filename = fields.Char(string='Nom du fichier (Contrat)')

    def _upload_file_to_nextcloud(self, field_name, file_name, file_data, record_id):
        """Upload file to Nextcloud and return the public share URL or error dict"""
        import requests
        import base64
        from requests.auth import HTTPBasicAuth
        nextcloud_url = "https://cloudsmart.webvue.tn/remote.php/dav/files/mharsikarim/"
        username = "mharsikarim"
        password = "Karimmh@1993"
        try:
            file_bytes = base64.b64decode(file_data)
            record = self.browse(int(record_id))
            folder_name = f"CentralProduction/{record.id}/"

            # Ensure all parts of the destination folder exist (create CentralProduction/ first, then CentralProduction/{record.id}/)
            folder_parts = [
                "CentralProduction/",
                f"CentralProduction/{record.id}/"
            ]
            for part in folder_parts:
                part_url = f"{nextcloud_url}{part}"
                mkcol_response = requests.request('MKCOL', part_url, auth=HTTPBasicAuth(username, password))
                # 201 = created, 405 = already exists, 409 = parent not found
                if mkcol_response.status_code not in (201, 405, 409):
                    return {'error': f"Nextcloud folder create failed for {part}: {mkcol_response.status_code}"}
                if mkcol_response.status_code == 409:
                    return {'error': f"Nextcloud parent folder missing for {part}. Please ensure parent path exists and credentials are correct."}

            upload_url = f"{nextcloud_url}{folder_name}{file_name}"
            response = requests.put(upload_url, data=file_bytes, auth=HTTPBasicAuth(username, password))
            if response.status_code not in (200, 201, 204):
                return {'error': f"Nextcloud upload failed: {response.status_code} - {response.text}"}

            # Create public share link
            share_api = "https://cloudsmart.webvue.tn/ocs/v2.php/apps/files_sharing/api/v1/shares"
            headers = {'OCS-APIRequest': 'true'}
            # Use full relative path from user root for sharing
            share_path = f"CentralProduction/{record.id}/{file_name}"
            data = {'path': share_path, 'shareType': 3, 'permissions': 1}
            resp = requests.post(share_api, headers=headers, data=data, auth=HTTPBasicAuth(username, password))
            if resp.status_code != 200:
                return {'error': f"Failed to create share link: {resp.status_code} - {resp.text}"}
            try:
                import xml.etree.ElementTree as ET
                # Nextcloud OCS returns XML by default
                root = ET.fromstring(resp.text)
                url_elem = root.find('.//url')
                if url_elem is None or not url_elem.text:
                    return {'error': f"Share API XML response has no <url>: {resp.text}"}
                url = url_elem.text
            except Exception as e:
                return {'error': f"Failed to parse share link XML response: {str(e)}. Raw response: {resp.text}"}
            record.sudo().write({field_name: url})
            return {'url': url}
        except Exception as e:
            return {'error': str(e)}

    def _nextcloud_auto_upload(self, vals):
        # Map field upload->final (binary->char)
        upload_map = [
            ('form_soum_upload', 'form_soum'),
            ('rapport_tech_econom_upload', 'rapport_tech_econom'),
            ('cap_fin_port_upload', 'cap_fin_port'),
            ('gar_banc_upload', 'gar_banc'),
            ('sourc_fin_upload', 'sourc_fin'),
            ('ref_tech_gpc_upload', 'ref_tech_gpc'),
            ('dem_avis_upload', 'dem_avis'),
            ('disp_site_upload', 'disp_site'),
            ('dem_acc_prel_upload', 'dem_acc_prel'),
            ('accor_steg_upload', 'accor_steg'),
            ('plan_terr_upload', 'plan_terr'),
            ('photos_upload', 'photos'),
            ('sim_pvsyst_upload', 'sim_pvsyst'),
            ('etud_env_upload', 'etud_env'),
            ('plan_trav_upload', 'plan_trav'),
            ('cah_charge_steg_upload', 'cah_charge_steg'),
            ('etud_tech_upload', 'etud_tech'),
            ('contrat_upload', 'contrat'),
        ]
        for upload_field, link_field in upload_map:
            if vals.get(upload_field):
                for record in self:
                    file_data = vals[upload_field]
                    # Attempt to extract original filename: look for <upload_field>_filename in vals
                    fname = vals.get(upload_field + '_filename')
                    if not fname:
                        # If no filename provided, try to get it from the record's existing filename field
                        existing_filename_field = upload_field + '_filename'
                        if hasattr(record, existing_filename_field):
                            fname = getattr(record, existing_filename_field)
                    if not fname:
                        # Last fallback: generate a generic filename with PDF extension
                        # Most document uploads in this context are likely PDFs
                        fname = f"{link_field}_{record.id or 'new'}.pdf"

                    # Ensure filename is properly encoded and doesn't contain invalid characters
                    import re
                    fname = re.sub(r'[<>:"/\\|?*]', '_', fname)  # Replace invalid filename characters
                    result = self._upload_file_to_nextcloud(link_field, fname, file_data.decode() if isinstance(file_data, bytes) else file_data, record.id or 0)
                    if 'url' in result:
                        vals[link_field] = result['url']
                    else:
                        raise UserError(result.get('error') or 'Unknown Nextcloud upload error')
                vals[upload_field] = False
        return vals

    def write(self, vals):
        vals = self._nextcloud_auto_upload(vals)
        return super(central_production, self).write(vals)

    @api.model
    def create(self, vals):
        rec = super(central_production, self).create(vals)
        rec._nextcloud_auto_upload(vals)
        return rec

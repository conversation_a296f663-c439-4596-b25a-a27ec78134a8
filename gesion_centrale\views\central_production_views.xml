<odoo>
    <!-- Tree View -->
    <record id="view_central_production_tree" model="ir.ui.view">
        <field name="name">central.production.tree</field>
        <field name="model">central.production</field>
        <field name="arch" type="xml">
            <tree>
                <field name="partner_id"/>
                <field name="company"/>
                <field name="user_id"/>
                <field name="puissance"/>
                <field name="date"/>
                <field name="adresse_terrain"/>
                <field name="montant_total"/>
                <field name="montant_financement_pers"/>
                <field name="montant_finance_banc"/>
            </tree>
        </field>
    </record>

    <!-- Form View -->
    <record id="view_central_production_form" model="ir.ui.view">
        <field name="name">central.production.form</field>
        <field name="model">central.production</field>
        <field name="arch" type="xml">
            <form>
                <sheet>
                    <group>
                        <field name="partner_id"/>
                        <field name="company"/>
                        <field name="user_id"/>
                        <field name="puissance"/>
                        <field name="date"/>
                        <field name="adresse_terrain"/>
                        <field name="montant_total" readonly="1"/>
                        <field name="montant_financement_pers"/>
                        <field name="montant_finance_banc"/>
                    </group>
                    <group>
                        <field name="form_soum_upload" filename="form_soum_upload_filename"/>
                        <field name="form_soum_upload_filename" invisible="1"/>
                        <field name="form_soum" widget="url"/>

                        <field name="rapport_tech_econom_upload" filename="rapport_tech_econom_upload_filename"/>
                        <field name="rapport_tech_econom_upload_filename" invisible="1"/>
                        <field name="rapport_tech_econom" widget="url"/>

                        <field name="cap_fin_port_upload" filename="cap_fin_port_upload_filename"/>
                        <field name="cap_fin_port_upload_filename" invisible="1"/>
                        <field name="cap_fin_port" widget="url"/>

                        <field name="gar_banc_upload" filename="gar_banc_upload_filename"/>
                        <field name="gar_banc_upload_filename" invisible="1"/>
                        <field name="gar_banc" widget="url"/>

                        <field name="sourc_fin_upload" filename="sourc_fin_upload_filename"/>
                        <field name="sourc_fin_upload_filename" invisible="1"/>
                        <field name="sourc_fin" widget="url"/>

                        <field name="ref_tech_gpc_upload" filename="ref_tech_gpc_upload_filename"/>
                        <field name="ref_tech_gpc_upload_filename" invisible="1"/>
                        <field name="ref_tech_gpc" widget="url"/>

                        <field name="dem_avis_upload" filename="dem_avis_upload_filename"/>
                        <field name="dem_avis_upload_filename" invisible="1"/>
                        <field name="dem_avis" widget="url"/>

                        <field name="disp_site_upload" filename="disp_site_upload_filename"/>
                        <field name="disp_site_upload_filename" invisible="1"/>
                        <field name="disp_site" widget="url"/>

                        <field name="dem_acc_prel_upload" filename="dem_acc_prel_upload_filename"/>
                        <field name="dem_acc_prel_upload_filename" invisible="1"/>
                        <field name="dem_acc_prel" widget="url"/>

                        <field name="accor_steg_upload" filename="accor_steg_upload_filename"/>
                        <field name="accor_steg_upload_filename" invisible="1"/>
                        <field name="accor_steg" widget="url"/>

                        <field name="plan_terr_upload" filename="plan_terr_upload_filename"/>
                        <field name="plan_terr_upload_filename" invisible="1"/>
                        <field name="plan_terr" widget="url"/>

                        <field name="photos_upload" filename="photos_upload_filename"/>
                        <field name="photos_upload_filename" invisible="1"/>
                        <field name="photos" widget="url"/>

                        <field name="sim_pvsyst_upload" filename="sim_pvsyst_upload_filename"/>
                        <field name="sim_pvsyst_upload_filename" invisible="1"/>
                        <field name="sim_pvsyst" widget="url"/>

                        <field name="etud_env_upload" filename="etud_env_upload_filename"/>
                        <field name="etud_env_upload_filename" invisible="1"/>
                        <field name="etud_env" widget="url"/>

                        <field name="plan_trav_upload" filename="plan_trav_upload_filename"/>
                        <field name="plan_trav_upload_filename" invisible="1"/>
                        <field name="plan_trav" widget="url"/>

                        <field name="cah_charge_steg_upload" filename="cah_charge_steg_upload_filename"/>
                        <field name="cah_charge_steg_upload_filename" invisible="1"/>
                        <field name="cah_charge_steg" widget="url"/>

                        <field name="etud_tech_upload" filename="etud_tech_upload_filename"/>
                        <field name="etud_tech_upload_filename" invisible="1"/>
                        <field name="etud_tech" widget="url"/>

                        <field name="contrat_upload" filename="contrat_upload_filename"/>
                        <field name="contrat_upload_filename" invisible="1"/>
                        <field name="contrat" widget="url"/>


                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Search View -->
    <record id="view_central_production_search" model="ir.ui.view">
        <field name="name">central.production.search</field>
        <field name="model">central.production</field>
        <field name="arch" type="xml">
            <search>
                <field name="partner_id"/>
                <field name="company"/>
                <field name="user_id"/>
                <field name="adresse_terrain"/>
                <field name="date"/>
                <field name="puissance"/>
                <separator/>
                <filter name="filter_date_today" string="Today" domain="[('date', '=', context_today())]"/>
                <filter name="filter_date_week" string="This Week" domain="[('date', '&gt;=', (context_today() - datetime.timedelta(days=7)).strftime('%Y-%m-%d'))]"/>
                <filter name="filter_date_month" string="This Month" domain="[('date', '&gt;=', (context_today().replace(day=1)).strftime('%Y-%m-%d'))]"/>
                <separator/>
                <group expand="0" string="Group By">
                    <filter name="group_by_partner" string="Client" context="{'group_by': 'partner_id'}"/>
                    <filter name="group_by_company" string="Société" context="{'group_by': 'company'}"/>
                    <filter name="group_by_user" string="Commercial" context="{'group_by': 'user_id'}"/>
                    <filter name="group_by_date" string="Date" context="{'group_by': 'date'}"/>
                </group>
            </search>
        </field>
    </record>
</odoo>